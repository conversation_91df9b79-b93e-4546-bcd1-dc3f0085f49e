<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Widget API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #8B5CF6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #7C3AED;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Eneco Chatbot Widget API Test</h1>
        
        <div class="test-section">
            <h3>Widget Lifecycle</h3>
            <button class="test-button" onclick="testInit()">Test init()</button>
            <button class="test-button" onclick="testDestroy()">Test destroy()</button>
            <button class="test-button" onclick="testIsInitialized()">Test isInitialized()</button>
            <div id="lifecycle-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Configuration</h3>
            <button class="test-button" onclick="testGetConfig()">Test getConfig()</button>
            <button class="test-button" onclick="testUpdateConfig()">Test updateConfig()</button>
            <div id="config-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Full Test Suite</h3>
            <button class="test-button" onclick="runFullTest()">Run All Tests</button>
            <div id="full-test-result" class="result"></div>
        </div>
    </div>

    <!-- Global Configuration -->
    <script>
        window.MyChatbotSettings = {
            assistantName: 'Edwin',
            assistantTitle: 'Eneco Assistant',
            placeholder: 'Ask me anything about Eneco...',
            theme: 'light',
            position: 'bottom-right',
            enableFullscreen: true,
            autoOpen: false,
            zIndex: 9999,
            apiUrl: 'https://bot-v1.acc.api-digital.enecogroup.com/api/eneco',
            customerId: 'test-customer',
            accountId: 'test-account'
        };

        function log(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            const timestamp = new Date().toLocaleTimeString();
            const className = isError ? 'error' : 'success';
            element.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
        }

        function testInit() {
            try {
                if (window.EnecoChatbot) {
                    window.EnecoChatbot.init();
                    log('lifecycle-result', '✓ init() called successfully');
                } else {
                    log('lifecycle-result', '✗ window.EnecoChatbot not found', true);
                }
            } catch (error) {
                log('lifecycle-result', `✗ init() error: ${error.message}`, true);
            }
        }

        function testDestroy() {
            try {
                if (window.EnecoChatbot) {
                    window.EnecoChatbot.destroy();
                    log('lifecycle-result', '✓ destroy() called successfully');
                } else {
                    log('lifecycle-result', '✗ window.EnecoChatbot not found', true);
                }
            } catch (error) {
                log('lifecycle-result', `✗ destroy() error: ${error.message}`, true);
            }
        }

        function testIsInitialized() {
            try {
                if (window.EnecoChatbot) {
                    const result = window.EnecoChatbot.isInitialized();
                    log('lifecycle-result', `✓ isInitialized() returned: ${result}`);
                } else {
                    log('lifecycle-result', '✗ window.EnecoChatbot not found', true);
                }
            } catch (error) {
                log('lifecycle-result', `✗ isInitialized() error: ${error.message}`, true);
            }
        }

        function testGetConfig() {
            try {
                if (window.EnecoChatbot) {
                    const config = window.EnecoChatbot.getConfig();
                    log('config-result', `✓ getConfig() returned: ${JSON.stringify(config, null, 2)}`);
                } else {
                    log('config-result', '✗ window.EnecoChatbot not found', true);
                }
            } catch (error) {
                log('config-result', `✗ getConfig() error: ${error.message}`, true);
            }
        }

        function testUpdateConfig() {
            try {
                if (window.EnecoChatbot) {
                    const newConfig = {
                        theme: 'dark',
                        assistantName: 'Updated Edwin'
                    };
                    window.EnecoChatbot.updateConfig(newConfig);
                    log('config-result', `✓ updateConfig() called with: ${JSON.stringify(newConfig, null, 2)}`);
                    
                    // Verify the update
                    const updatedConfig = window.EnecoChatbot.getConfig();
                    log('config-result', `✓ Updated config: ${JSON.stringify(updatedConfig, null, 2)}`);
                } else {
                    log('config-result', '✗ window.EnecoChatbot not found', true);
                }
            } catch (error) {
                log('config-result', `✗ updateConfig() error: ${error.message}`, true);
            }
        }

        function runFullTest() {
            const resultElement = document.getElementById('full-test-result');
            resultElement.innerHTML = '';
            
            log('full-test-result', 'Starting full test suite...');
            
            // Test 1: Check if widget is loaded
            if (!window.EnecoChatbot) {
                log('full-test-result', '✗ FAIL: window.EnecoChatbot not found', true);
                return;
            }
            log('full-test-result', '✓ PASS: window.EnecoChatbot exists');

            // Test 2: Test all methods exist
            const methods = ['init', 'destroy', 'isInitialized', 'getConfig', 'updateConfig'];
            for (const method of methods) {
                if (typeof window.EnecoChatbot[method] === 'function') {
                    log('full-test-result', `✓ PASS: ${method} method exists`);
                } else {
                    log('full-test-result', `✗ FAIL: ${method} method missing`, true);
                }
            }

            // Test 3: Test method calls
            try {
                const isInit1 = window.EnecoChatbot.isInitialized();
                log('full-test-result', `✓ PASS: isInitialized() = ${isInit1}`);

                const config1 = window.EnecoChatbot.getConfig();
                log('full-test-result', `✓ PASS: getConfig() returned object with ${Object.keys(config1).length} properties`);

                window.EnecoChatbot.destroy();
                log('full-test-result', '✓ PASS: destroy() executed');

                const isInit2 = window.EnecoChatbot.isInitialized();
                log('full-test-result', `✓ PASS: isInitialized() after destroy = ${isInit2}`);

                window.EnecoChatbot.init();
                log('full-test-result', '✓ PASS: init() executed');

                const isInit3 = window.EnecoChatbot.isInitialized();
                log('full-test-result', `✓ PASS: isInitialized() after init = ${isInit3}`);

                window.EnecoChatbot.updateConfig({ theme: 'dark' });
                log('full-test-result', '✓ PASS: updateConfig() executed');

                log('full-test-result', '🎉 ALL TESTS PASSED!');
            } catch (error) {
                log('full-test-result', `✗ FAIL: Test execution error: ${error.message}`, true);
            }
        }

        // Auto-run basic check when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                if (window.EnecoChatbot) {
                    log('lifecycle-result', '✓ Widget loaded successfully');
                } else {
                    log('lifecycle-result', '✗ Widget failed to load', true);
                }
            }, 1000);
        });
    </script>

    <!-- Load the widget -->
    <script src="./dist/eneco-chatbot-widget.iife.js"></script>
</body>
</html>
